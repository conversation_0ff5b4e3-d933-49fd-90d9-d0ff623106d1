import { getLogger } from '../utils';
import { IWrenAIAdaptor } from '../adaptors/wrenAIAdaptor';
import {
  MonitorGenerationRepository,
  MonitorChatRefinementRepository,
} from '../repositories';
import { MonitorStatus } from '../models/monitor';

const logger = getLogger('MonitorBackgroundTracker');

interface TrackedMonitorTask {
  queryId: string;
  type: 'generation' | 'chat_refinement';
  relatedId: number;
  threadResponseId?: number;
  lastStatus: string;
  addedAt: number;
  lastPolled: number;
}

export class MonitorBackgroundTracker {
  private tasks: Map<string, TrackedMonitorTask> = new Map();
  private pollingInterval: number = 1000; // 1 second
  private runningJobs = new Set<string>();
  private intervalId?: NodeJS.Timeout;
  private retentionTime = 5 * 60 * 1000; // 5 minutes

  constructor(
    private wrenAIAdaptor: IWrenAIAdaptor,
    private monitorGenerationRepository: MonitorGenerationRepository,
    private monitorChatRefinementRepository: MonitorChatRefinementRepository,
  ) {
    this.startPolling();
    this.loadUnfinishedTasks();
  }

  private startPolling() {
    logger.info('Starting monitor background tracker polling');
    this.intervalId = setInterval(() => {
      this.pollTasks();
    }, this.pollingInterval);
  }

  private async pollTasks() {
    const jobs = Array.from(this.tasks.values()).map((task) => async () => {
      // Skip if already running
      if (this.runningJobs.has(task.queryId)) {
        return;
      }

      this.runningJobs.add(task.queryId);

      try {
        // Poll AI service for result
        const result = await this.wrenAIAdaptor.getMonitorGenerationResult(
          task.queryId,
        );

        // Update last polled time
        task.lastPolled = Date.now();

        // Check for status changes
        if (task.lastStatus === result.status) {
          return;
        }

        // Update database with new status and data
        await this.updateTaskInDatabase(task, result);

        // Update task status
        task.lastStatus = result.status;

        logger.debug(
          `Updated ${task.type} task ${task.queryId} with status ${result.status}`,
        );

        // Cleanup if finalized
        if (this.isFinalized(result.status)) {
          logger.info(
            `Task ${task.queryId} finalized with status ${result.status}`,
          );
          this.tasks.delete(task.queryId);
        }
      } catch (error: any) {
        logger.error(`Error polling task ${task.queryId}: ${error.message}`);

        // Don't remove task on error - keep trying
        // But avoid infinite polling of permanently failed tasks
        if (task.addedAt < Date.now() - this.retentionTime) {
          logger.warn(
            `Removing stale task ${task.queryId} after retention time`,
          );
          this.tasks.delete(task.queryId);
        }
      } finally {
        this.runningJobs.delete(task.queryId);
      }
    });

    // Run all jobs in parallel
    Promise.allSettled(jobs.map((job) => job()));

    // Cleanup stale tasks
    this.cleanupStaleTasks();
  }

  private async updateTaskInDatabase(task: TrackedMonitorTask, result: any) {
    const updateData: any = {
      status: result.status?.toUpperCase(),
    };

    // Add result data if available
    if (result.response) {
      updateData.reasoningSteps = result.response.reasoning_steps;
      updateData.validationResults = result.response.validation_results;
      updateData.keyInsights = result.response.validation_results?.key_insights;
    }

    // Add error information if task failed
    if (result.error) {
      updateData.status = MonitorStatus.FAILED;
      updateData.reasoningSteps = updateData.reasoningSteps || [];
      updateData.reasoningSteps.push(
        `Error: ${result.error.message || 'Unknown error'}`,
      );
    }

    if (task.type === 'generation') {
      // Add monitor-specific fields
      if (result.response) {
        updateData.monitorSql = result.response.monitor_sql;
        updateData.expressionSuggestions = result.response.template_suggestions;
      }

      await this.monitorGenerationRepository.updateByTaskId(
        task.queryId,
        updateData,
      );
    } else if (task.type === 'chat_refinement') {
      // Add chat refinement-specific fields
      if (result.response) {
        updateData.refinedSql = result.response.refined_sql;
      }

      await this.monitorChatRefinementRepository.updateByTaskId(
        task.queryId,
        updateData,
      );
    }
  }

  private cleanupStaleTasks() {
    const now = Date.now();
    const staleTasks = Array.from(this.tasks.entries()).filter(
      ([_, task]) =>
        task.addedAt < now - this.retentionTime &&
        this.isFinalized(task.lastStatus),
    );

    staleTasks.forEach(([queryId, _task]) => {
      logger.debug(`Cleaning up stale task ${queryId}`);
      this.tasks.delete(queryId);
    });
  }

  private isFinalized(status: string): boolean {
    return [
      MonitorStatus.FINISHED,
      MonitorStatus.FAILED,
      MonitorStatus.STOPPED,
    ].includes(status as MonitorStatus);
  }

  public addMonitorGenerationTask(
    queryId: string,
    threadResponseId: number,
    monitorId: number,
  ) {
    logger.debug(
      `Adding monitor generation task ${queryId} for thread response ${threadResponseId}`,
    );

    this.tasks.set(queryId, {
      queryId,
      type: 'generation',
      relatedId: monitorId,
      threadResponseId,
      lastStatus: MonitorStatus.PENDING,
      addedAt: Date.now(),
      lastPolled: Date.now(),
    });
  }

  public addChatRefinementTask(
    queryId: string,
    monitorGenerationId: number,
    refinementId: number,
  ) {
    logger.debug(
      `Adding chat refinement task ${queryId} for monitor generation ${monitorGenerationId}`,
    );

    this.tasks.set(queryId, {
      queryId,
      type: 'chat_refinement',
      relatedId: refinementId,
      lastStatus: MonitorStatus.PENDING,
      addedAt: Date.now(),
      lastPolled: Date.now(),
    });
  }

  public removeTask(queryId: string) {
    logger.debug(`Removing task ${queryId}`);
    this.tasks.delete(queryId);
  }

  public getTaskStatus(queryId: string): string | null {
    const task = this.tasks.get(queryId);
    return task ? task.lastStatus : null;
  }

  public getActiveTaskCount(): number {
    return this.tasks.size;
  }

  private async loadUnfinishedTasks() {
    // Load unfinished monitor generation tasks from database
    try {
      const unfinishedGenerations =
        await this.monitorGenerationRepository.findByStatus([
          MonitorStatus.PENDING,
          MonitorStatus.UNDERSTANDING,
          MonitorStatus.REASONING,
          MonitorStatus.GENERATING,
          MonitorStatus.VALIDATING,
        ]);

      unfinishedGenerations.forEach((generation) => {
        if (generation.taskId) {
          this.tasks.set(generation.taskId, {
            queryId: generation.taskId,
            type: 'generation',
            relatedId: generation.id,
            threadResponseId: generation.threadResponseId,
            lastStatus: generation.status,
            addedAt: Date.now(),
            lastPolled: Date.now(),
          });
        }
      });

      // Load unfinished chat refinement tasks
      const unfinishedRefinements =
        await this.monitorChatRefinementRepository.findByStatus([
          MonitorStatus.PENDING,
          MonitorStatus.UNDERSTANDING,
          MonitorStatus.REASONING,
          MonitorStatus.GENERATING,
          MonitorStatus.VALIDATING,
        ]);

      unfinishedRefinements.forEach((refinement) => {
        if (refinement.taskId) {
          this.tasks.set(refinement.taskId, {
            queryId: refinement.taskId,
            type: 'chat_refinement',
            relatedId: refinement.id,
            lastStatus: refinement.status,
            addedAt: Date.now(),
            lastPolled: Date.now(),
          });
        }
      });

      logger.info(
        `Loaded ${unfinishedGenerations.length} unfinished generation tasks and ${unfinishedRefinements.length} unfinished refinement tasks`,
      );
    } catch (error: any) {
      logger.error(`Error loading unfinished tasks: ${error.message}`);
    }
  }

  public stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
    logger.info('Monitor background tracker stopped');
  }

  public getTasksInfo(): { [key: string]: any } {
    const tasks: { [key: string]: any } = {};
    this.tasks.forEach((task, queryId) => {
      tasks[queryId] = {
        type: task.type,
        relatedId: task.relatedId,
        status: task.lastStatus,
        addedAt: new Date(task.addedAt).toISOString(),
        lastPolled: new Date(task.lastPolled).toISOString(),
      };
    });
    return tasks;
  }
}
